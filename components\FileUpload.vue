<template>
  <div class="file-upload-wrapper">
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :multiple="multiple"
      style="display: none"
      @change="handleFileChange"
    />
    
    <div
      class="file-upload-area"
      :class="{ 'drag-over': isDragOver }"
      @click="openFileDialog"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <slot>
        <div class="default-upload-content">
          <span>Click to upload or drag and drop</span>
        </div>
      </slot>
    </div>
    
    <div v-if="files.length > 0" class="file-list">
      <div v-for="file in files" :key="file.id" class="file-item">
        <span>{{ file.name }}</span>
        <button @click="removeFile(file.id)" class="remove-btn">×</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface FileItem {
  id: string
  name: string
  size: number
  type: string
  file: File
  url?: string
}

interface Props {
  modelValue?: FileItem[]
  accept?: string
  multiple?: boolean
  extensions?: string[]
  drop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  accept: '*/*',
  multiple: false,
  extensions: () => [],
  drop: true,
})

const emit = defineEmits<{
  'update:modelValue': [files: FileItem[]]
  'input-file': [newFile: FileItem | null, oldFile: FileItem | null]
  'input-filter': [newFile: FileItem, oldFile: FileItem | null, prevent: () => void]
}>()

const fileInputRef = ref<HTMLInputElement>()
const isDragOver = ref(false)
const files = ref<FileItem[]>(props.modelValue)

const dropActive = computed(() => isDragOver.value && props.drop)

// Watch for external changes to modelValue
watch(() => props.modelValue, (newFiles) => {
  files.value = newFiles
}, { deep: true })

// Watch for internal changes to files
watch(files, (newFiles) => {
  emit('update:modelValue', newFiles)
}, { deep: true })

const openFileDialog = () => {
  fileInputRef.value?.click()
}

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const selectedFiles = Array.from(target.files || [])
  
  selectedFiles.forEach(file => {
    addFile(file)
  })
  
  // Reset input
  target.value = ''
}

const addFile = (file: File) => {
  const fileItem: FileItem = {
    id: generateId(),
    name: file.name,
    size: file.size,
    type: file.type,
    file,
  }
  
  // Create object URL for preview
  if (file.type.startsWith('image/')) {
    fileItem.url = URL.createObjectURL(file)
  }
  
  // Check file extension if specified
  if (props.extensions.length > 0) {
    const extension = file.name.split('.').pop()?.toLowerCase()
    if (!extension || !props.extensions.includes(extension)) {
      const prevent = () => {}
      emit('input-filter', fileItem, null, prevent)
      return
    }
  }
  
  const oldFile = files.value.find(f => f.name === file.name) || null
  
  // Call input-filter event
  let prevented = false
  const prevent = () => { prevented = true }
  emit('input-filter', fileItem, oldFile, prevent)
  
  if (prevented) return
  
  if (!props.multiple) {
    files.value = [fileItem]
  } else {
    files.value.push(fileItem)
  }
  
  emit('input-file', fileItem, oldFile)
}

const removeFile = (id: string) => {
  const fileIndex = files.value.findIndex(f => f.id === id)
  if (fileIndex > -1) {
    const oldFile = files.value[fileIndex]
    
    // Revoke object URL to prevent memory leaks
    if (oldFile.url) {
      URL.revokeObjectURL(oldFile.url)
    }
    
    files.value.splice(fileIndex, 1)
    emit('input-file', null, oldFile)
  }
}

const handleDragOver = (event: DragEvent) => {
  if (!props.drop) return
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  if (!props.drop) return
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  if (!props.drop) return
  isDragOver.value = false
  
  const droppedFiles = Array.from(event.dataTransfer?.files || [])
  droppedFiles.forEach(file => {
    addFile(file)
  })
}

const generateId = () => {
  return Math.random().toString(36).substr(2, 9)
}

const clear = () => {
  files.value.forEach(file => {
    if (file.url) {
      URL.revokeObjectURL(file.url)
    }
  })
  files.value = []
}

const update = (id: string, updates: Partial<FileItem>) => {
  const fileIndex = files.value.findIndex(f => f.id === id)
  if (fileIndex > -1) {
    files.value[fileIndex] = { ...files.value[fileIndex], ...updates }
  }
}

// Expose methods for template ref access
defineExpose({
  clear,
  update,
  dropActive,
})
</script>

<style scoped>
.file-upload-wrapper {
  width: 100%;
}

.file-upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.file-upload-area:hover,
.file-upload-area.drag-over {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.default-upload-content {
  color: #666;
}

.file-list {
  margin-top: 10px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 5px;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: #c82333;
}
</style>
