import axios, { AxiosInstance } from 'axios'

export function useAPI() {
  const apiClient: AxiosInstance = axios.create({
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // Request interceptor
  apiClient.interceptors.request.use(
    (config) => {
      // Add any global request configuration here
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // Response interceptor
  apiClient.interceptors.response.use(
    (response) => {
      return response
    },
    (error) => {
      // Handle global error responses here
      return Promise.reject(error)
    }
  )

  return {
    apiClient,
  }
}
