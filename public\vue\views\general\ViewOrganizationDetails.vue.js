var viewOrganizationDetails = Vue.component('view-org-details', {
    template: `
        <section class="component-padding" style="margin-top:45px">
            <v-card class="card-radius view-org-details-card">
                <v-card-title class="d-flex justify-end">
                    <div v-if="isViewPage && reportLogoURL && !showSave">
                        <v-btn v-if="rolesResponse.Role_Update" rounded color="primary" @click="showEditForm()">
                                Edit
                        </v-btn>
                        <v-btn style="cursor : not-allowed" v-else rounded v-tooltip="{content: 'You dont have edit access rights',trigger: 'hover',placement: 'top' }">
                                Edit
                        </v-btn>
                    </div>
                    <div v-else-if="!reportLogoURL && isViewPage && !showSave">
                        <v-btn v-if="rolesResponse.Role_Update" rounded color="primary" @click="saveOrgDetails()">
                                Save
                        </v-btn>
                        <v-btn v-else style="cursor : not-allowed" rounded v-tooltip="{content: 'You dont have edit access rights',trigger: 'hover',placement: 'top' }">
                            Save
                        </v-btn>
                    </div>
                    <div v-else>
                        <v-btn rounded outlined color="primary mr-5" @click="closeEditForm()">
                                Cancel
                        </v-btn>
                        <v-btn v-if="rolesResponse.Role_Update" rounded color="primary" @click="saveOrgDetails()">
                                Save
                        </v-btn>
                        <v-btn v-else style="cursor : not-allowed" rounded v-tooltip="{content: 'You dont have edit access rights',trigger: 'hover',placement: 'top' }">
                            Save
                        </v-btn>
                    </div>
                </v-card-title>
                <v-card-text class="view-card-text">
                    <div class="secondaryColor font-weight-bold body-1">
                        Report Logo                        
                    </div>
                    <div class="example-avatar" style="display: flex">
                        <div v-show="$refs.upload && $refs.upload.dropActive" class="drop-active">
                            <h3>Drop files to upload</h3>
                        </div>
                        <div class="avatar-upload"  v-show="!edit">
                            <div class="text-center p-10">
                                <label for="avatar">
                                    <img :src="reportLogoSrc"  class="rounded-circle" @error="errorReportImg"/>
                                </label>
                            </div>
                            <div class="text-center p-2" v-if="(!reportLogoURL && isViewPage) || (reportLogoURL && !isViewPage)">
                                <file-upload
                                extensions="jpg,jpeg,png"
                                accept="image/png,image/jpeg"
                                name="report logo"
                                class="btn btn-secondary"
                                :drop="!edit"
                                v-model="files"
                                @input-filter="inputFilter"
                                @input-file="inputFile"
                                ref="upload">
                                <span style="text-transform: capitalize !important;">{{uploadBtnText}}</span>
                                </file-upload>
                                <v-btn v-if="(rolesResponse.Role_Update) && reportLogoURL" rounded color="primary" @click="removeLogo()">
                                    Remove Logo
                                </v-btn>
                                <v-btn v-else-if="reportLogoURL" style="cursor : not-allowed" rounded v-tooltip="{content: 'You dont have edit access rights',trigger: 'hover',placement: 'top' }">
                                    Remove Logo
                                </v-btn>
                            </div>
                            <v-switch
                                v-if="!reportLogoURL && isViewPage && !showSave""
                                color="primary"
                                class="p-2"
                                :disabled = "!reportLogoURL && !uploadFileName"
                                label="Use Report Logo As Product Logo"
                                v-model="isProductLogo"
                                @change="isFormDirty = true"
                                :true-value="1"
                                :false-value="0"
                            ></v-switch>
                            <v-switch
                                v-else
                                color="primary"
                                class="p-2"
                                :disabled = "!rolesResponse.Role_Update || !reportLogoURL || isViewPage"
                                label="Use Report Logo As Product Logo"
                                v-model="isProductLogo"
                                @change="isFormDirty = true"
                                :true-value="1"
                                :false-value="0"
                            ></v-switch>
                        </div>
                    </div>
                    <div class="avatar-edit" v-show="files.length && edit" style="over-flow: hidden">
                    <div class="avatar-edit-image" v-if="files.length" style="width: 100%; height: 400px">
                        <img ref="editImage" :src="files[0].url" />
                    </div>
                    <div class="text-center p-10">
                        <v-btn rounded outlined color="primary mr-5" @click.prevent="$refs.upload.clear">Cancel</v-btn>
                        <v-btn rounded color="primary" type="submit" @click.prevent="editSave">Save</v-btn>
                    </div>
                    </div>
                <v-overlay
                    :absolute="true"
                    :value="isLoading || showFormDirty"
                    opacity = '0.9'
                    color = "#fff">
                    <v-progress-circular v-if="isLoading" color='primary' indeterminate size="64"></v-progress-circular>
                    <div v-else>
                        <v-row class="set-layout">
                            <v-col cols="12" class="d-flex flex-column justify-center align-center">
                                <div class="mb-5 d-flex flex-column justify-center align-center">
                                    <img width="60%" height="auto" :src="basePath+'vue/assets/images/exit_form.webp'" @error="imageFormatSwap" alt="No Records">
                                </div>
                                <span class="mb-5 sub-content view-error-meesage primary--text" style="text-align: center">Your changes won't be saved. Are you sure want to close this form?</span>
                                <div class="mb-4">
                                    <v-row>
                                        <v-col xl="6" lg="6" md="6" sm="6" cols="6">
                                            <v-btn rounded color="primary" @click="showFormDirty=false">
                                                    No
                                            </v-btn>
                                        </v-col>
                                        <v-col xl="6" lg="6" md="6" sm="6" cols="6">
                                            <v-btn rounded outlined color="primary" @click="confirmFormClose()">
                                                    Yes
                                            </v-btn>
                                        </v-col>
                                    </v-row>
                                </div>
                            </v-col>
                        </v-row>
                    </div>
                </v-overlay>
                </v-card-text>
            </v-card>
            
        </section>
        `,

    props: {

        logoUrl: {
            required: true,
            type: String    
        },

        orgCode : {
            type : String,
            required : true
        },

        employeeId : {
            type : Number,
            required : true
        },

        renderCount : {
            type : Number,
            required : true
        },

        rolesResponse : {
            type : Object,
            required : true
        },

        basePath : {
            type : String,
            required : true
        },

        atsBaseUrl : {
            type : String,
            required : true
        },

        apiHeaders: {
            type: Object,
            required: true,
        },
        useReportLogoAsProductLogo: {
            type: String,
            required: true,
        },
        reportLogoPath: {
            type: String,
            required: false,
        },
    },

    data: function () {
        
        return {
            isFormDirty : false,
            isLoading : false,
            showFormDirty : false,
            isViewPage: true,
            showSave: false,
            reportLogoURL: '',
            files: [],
            edit: false,
            cropper: false,
            uploadFileName: '',
            uploadedFile: '',
            operationType: 'add',
            isProductLogo: 0,
        };
    },

    computed: {
        uploadBtnText() {
            if (!this.reportLogoURL && this.files.length === 0) {
                return 'Set Logo';
            } else {
                return 'Change';
            }
        },
        reportLogoSrc() {
            if (this.operationType !== 'remove') {
                if (this.files.length) {
                    return this.files[0].url
                }
                return this.reportLogoURL ? this.reportLogoURL : this.basePath + 'vue/assets/images/empty-image.webp'
            } else {
                return this.basePath + 'vue/assets/images/empty-image.webp'
            }
        },
        errorReportImg() {
            if (this.operationType !== 'remove') {
                if (this.files.length) {
                    return this.files[0].url
                }
                return this.reportLogoURL ? this.reportLogoURL : this.basePath + 'vue/assets/images/empty-image.png'
            } else {
                return this.basePath + 'vue/assets/images/empty-image.png'
            }
        }
    },

    mounted: function () {
        this.reportLogoURL = this.logoUrl;
        this.isProductLogo =  this.useReportLogoAsProductLogo && this.useReportLogoAsProductLogo.toLowerCase() === 'yes' ? 1 : 0;
        if (this.renderCount == 1) {
            let styleElem = document.createElement('style');
            styleElem.textContent = `
                .component-padding{
                    padding: 50px;
                }

                .view-org-details-card {
                    padding: 2em;
                    min-height : 600px;
                }

                .view-error-meesage {
                    font-size: 1.4em !important;
                    font-weight: 500;
                }
                label {
                    margin-bottom : 0px !important
                }

                .v-application--is-ltr .v-messages{
                    display : contents !important
                }
                @media screen and (max-width:600px) {
                    .component-padding{
                        padding-left: 25px !important;
                        padding-right: 25px !important;
                    }
                }
                .example-avatar .avatar-upload .rounded-circle {
                    width: 200px;
                }
                .example-avatar .text-center .btn {
                    margin: 0 .5rem
                }
                .example-avatar .avatar-edit-image {
                    max-width: 100%
                }
                .example-avatar .drop-active {
                    top: 0;
                    bottom: 0;
                    right: 0;
                    left: 0;
                    position: fixed;
                    z-index: 9999;
                    opacity: .6;
                    text-align: center;
                    background: #000;
                }
                .example-avatar .drop-active h3 {
                    margin: -.5em 0 0;
                    position: absolute;
                    top: 50%;
                    left: 0;
                    right: 0;
                    -webkit-transform: translateY(-50%);
                    -ms-transform: translateY(-50%);
                    transform: translateY(-50%);
                    font-size: 40px;
                    color: #fff;
                    padding: 0;
                }
            `;
            document.head.appendChild(styleElem);
        }
    },
    watch: {
        edit(value) {
            if (value) {
                this.$nextTick(function () {
                if (!this.$refs.editImage) {
                    return
                }
                let cropper = new Cropper(this.$refs.editImage, {
                    aspectRatio: 16 / 9,
                    viewMode: 1,
                })
                this.cropper = cropper
                })
            } else {
                if (this.cropper) {
                this.cropper.destroy()
                this.cropper = false
                }
            }
        },
        files() {
            if (this.files.length > 0) {
                this.uploadBtnText = 'Change';
            } else {
                this.uploadBtnText = 'Set Logo'
            }
        },
    },
  
    methods: {
        atsGraphQl(){
            return graphql(this.atsBaseUrl, {
                method: 'POST',
                headers: this.apiHeaders,
                asJSON: true
            });
        },
        baseUrl(){
            var pathParts = location.pathname.split('/');
            if (localStorage.getItem('production') == 0) {
                var url = location.origin + '/' + pathParts[1].trim('/') + '/'; // http://localhost/hrapponline/
            } else {
                var url = location.origin + '/'; // http://subdomain.hrapp.co
            }
            return url;
        },
        // open edit form
        showEditForm() {
            this.showSave = true;
            this.isViewPage = false;
            this.operationType = 'edit';
        },
        // close the edit form
        closeEditForm() {
            if (this.isFormDirty && this.operationType !== 'remove') {
                this.showFormDirty = true;
            } else {
                this.isFormDirty = false;
                this.showFormDirty = false;
                this.isViewPage = true;
                this.showSave = false;
                this.files = [];
                this.edit = false;
                this.operationType = 'edit';
                this.reportLogoURL = this.logoUrl; 
            }
        },
        // confirm closing edit form
        confirmFormClose() {
            this.isFormDirty = false;
            this.showFormDirty = false;
            this.closeEditForm();
        },
        // check form has any changes
        saveOrgDetails() {
            if (this.isFormDirty) {
                this.updateOrgDetails();
            } else {
                this.$emit('handle-custom-error',  'Form has no changes')
            }
        },
        // update tax config data to BE
        updateOrgDetails() {
            let self = this;
                try {
                    self.isLoading = true;
                    let atsGraphQl = self.atsGraphQl();
                    if (this.operationType !== 'remove') {
                        if(self.uploadFileName && !self.edit) {
                            var fileUpload = atsGraphQl(`mutation(
                                $fileName: String!,
                                $action: String!,
                                $type:String) {
                                    getPresignedUrl 
                                    (
                                        fileName:$fileName,
                                        action:$action,
                                        type:$type
                                    ) 
                                    { 
                                            errorCode message presignedUrl
                                    }
                            }
                            `);
                            fileUpload({
                                "fileName": "hrapp_upload/" + self.orgCode + "_tmp/logos/" + self.uploadFileName,
                                "action" : "upload",
                                "type" : "logo"
                            }).then(function (response) {
                                if (response.getPresignedUrl && response.getPresignedUrl.presignedUrl) {
                                    self.reportLogoURL = response.getPresignedUrl.presignedUrl;
                                    // upload file in s3 using presignedurl
                                    axios.put(response.getPresignedUrl.presignedUrl, self.uploadedFile).then(res => {  
                                        self.updateReportLogo();
                                    }).catch(fileUploadErr => {
                                        self.isLoading = false;
                                        self.$emit('handle-custom-error', 'Unable to upload report logo');
                                    })
                                } else {
                                    self.isLoading = false;
                                    self.$emit('handle-custom-error', 'Unable to upload report logo');
                                }                    
                            }).catch(fileUploadErr => {
                                self.isLoading = false;
                                self.$emit('handle-error', fileUploadErr)
                            });
                        } else{
                            if(!self.edit){
                                self.updateReportLogo();
                            } else {
                                self.isLoading = false;
                                this.$emit('handle-custom-error',  'Form has no changes')
                            }
                        }
                    } else {                    
                        var fileRemove = atsGraphQl(`mutation(
                            $fileName: String!,
                            $type:String) {
                                deleteS3Files
                                (
                                    fileName:$fileName,
                                    type:$type
                                ) 
                                { 
                                    errorCode 
                                    message
                                }
                            }
                        `);
                        fileRemove({
                            "fileName": "hrapp_upload/" + self.orgCode + "_tmp/logos/" + self.uploadFileName,
                            "type": "logo"
                        }).then(function (response) {
                            // list the types of LOP
                            axios.post(self.baseUrl() + 'organization/organization-settings/remove-logo/orgCode/'+ self.orgCode,  {
                            }).then(removeLogoResponse => {
                                self.reportLogoURL = '';
                                if (removeLogoResponse.data.success) {
                                    self.isFormDirty = false;
                                    self.isViewPage = true;
                                    self.showSave = false;
                                    self.files = [];
                                    self.$emit('handle-success', removeLogoResponse.data.msg);
                                }else{
                                    self.$emit('handle-custom-error', removeLogoResponse.data.msg)
                                }
                                self.isLoading = false;
                            }).catch(removeLogoError => {
                                self.isLoading = false;
                                self.$emit('handle-custom-error', 'Unable to delete report logo');
                            })
                        }).catch(function (err) {
                            self.isLoading = false;
                            self.$emit('handle-custom-error', 'Unable to delete report logo');
                        })
                    }
                } catch (fileUploadErr) {
                    self.$emit('handle-error', fileUploadErr);
                }
        },
        // called when remove logo is triggered
        removeLogo() {
            this.isFormDirty = true;
            this.showSave = true;
            this.operationType = 'remove';
        },
        //function to switch image to png format if browser not support webp
        imageFormatSwap(e){
            e.target.src=this.basePath+'vue/assets/images/exit_form.png';
        },
        editSave() {
            this.edit = false;
            this.isFormDirty = true;
            this.operationType = 'edit';
            let oldFile = this.files[0]
            let binStr = atob(this.cropper.getCroppedCanvas().toDataURL(oldFile.type).split(',')[1])
            let arr = new Uint8Array(binStr.length)
            for (let i = 0; i < binStr.length; i++) {
                arr[i] = binStr.charCodeAt(i)
            }
            let file = new File([arr], oldFile.name, { type: oldFile.type })
            this.$refs.upload.update(oldFile.id, {
                file,
                type: file.type,
                size: file.size,
                active: true,
            })
            this.uploadedFile = file;
            let fileName = file.name;
            let reportLogoImageExtension = fileName.split('.');
            this.uploadFileName = this.orgCode + "-Report_LogoPath" + "." + reportLogoImageExtension[1];
        },
        inputFile(newFile, oldFile, prevent) {
            if (newFile && !oldFile) {
            this.$nextTick(function () {
                this.edit = true
            })
            }
            if (!newFile && oldFile) {
            this.edit = false
            }
        },
        inputFilter(newFile, oldFile, prevent) {
            if (newFile && !oldFile) {
            if (!/\.(gif|jpg|jpeg|png)$/i.test(newFile.name)) {
                this.$emit('handle-custom-error', 'Invalid file type selected');
                return prevent()
            }
            }
            if (newFile && (!oldFile || newFile.file !== oldFile.file)) {
            newFile.url = ''
            let URL = window.URL || window.webkitURL
            if (URL && URL.createObjectURL) {
                newFile.url = URL.createObjectURL(newFile.file)
            }
            }
        },
        updateReportLogo(){
            let self = this;
            axios.post(self.baseUrl() + 'settings/general/update-report-logo-name/', {
                logo: 'ReportLogo',
                uploadSuccessFilesArr: self.uploadFileName ? self.uploadFileName : self.reportLogoPath,
                useReportLogoAsProductLogo: self.isProductLogo == 0 ? 'No' : 'Yes'
            }).then(updateLogoResponse => {
                if (updateLogoResponse.data.success) {
                    self.isFormDirty = false;
                    self.isViewPage = true;
                    self.showSave = false;
                    self.$emit('handle-success', updateLogoResponse.data.msg);
                }else{
                    self.$emit('handle-custom-error', updateLogoResponse.data.msg)
                }
                self.isLoading = false;
            }).catch(updateLogoError => {
                self.isLoading = false;
                self.$emit('handle-custom-error', 'Unable to upload report logo');
            })
        }
    },


});
