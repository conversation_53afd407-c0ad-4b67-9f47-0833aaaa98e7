<template>
  <v-app>
    <v-main>
      <ViewOrganizationDetails
        :logo-url="logoUrl"
        :org-code="orgCode"
        :employee-id="employeeId"
        :render-count="1"
        :roles-response="rolesResponse"
        :base-path="basePath"
        :ats-base-url="atsBaseUrl"
        :api-headers="apiHeaders"
        :use-report-logo-as-product-logo="useReportLogoAsProductLogo"
        :report-logo-path="reportLogoPath"
        @handle-custom-error="handleCustomError"
        @handle-error="handleError"
        @handle-success="handleSuccess"
      />
    </v-main>
    
    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="snackbar.timeout"
    >
      {{ snackbar.message }}
      <template #actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-app>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ViewOrganizationDetails from './views/ViewOrganizationDetails.vue'

// Mock data - replace with actual data from your application
const logoUrl = ref('https://example.com/logo.png')
const orgCode = ref('ORG001')
const employeeId = ref(12345)
const rolesResponse = ref({
  Role_Update: true,
  Role_View: true,
})
const basePath = ref('/')
const atsBaseUrl = ref('https://api.example.com/ats/graphql')
const apiHeaders = ref({
  'Authorization': 'Bearer your-token-here',
  'Content-Type': 'application/json',
})
const useReportLogoAsProductLogo = ref('No')
const reportLogoPath = ref('')

// Snackbar state
const snackbar = ref({
  show: false,
  message: '',
  color: 'success',
  timeout: 4000,
})

// Event handlers
const handleCustomError = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: 'error',
    timeout: 6000,
  }
}

const handleError = (error: any) => {
  console.error('Error:', error)
  snackbar.value = {
    show: true,
    message: 'An unexpected error occurred',
    color: 'error',
    timeout: 6000,
  }
}

const handleSuccess = (message: string) => {
  snackbar.value = {
    show: true,
    message,
    color: 'success',
    timeout: 4000,
  }
}
</script>

<style>
/* Global styles */
.bg_grey_lighten1 {
  background-color: #f5f5f5;
}
</style>
