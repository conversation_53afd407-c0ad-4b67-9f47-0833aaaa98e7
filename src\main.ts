import { createApp } from 'vue'
import { createVuetify } from 'vuetify'
import { createPinia } from 'pinia'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

// Styles
import 'vuetify/styles'
import '@mdi/font/css/materialdesignicons.css'

import App from './App.vue'

// Create Vuetify instance
const vuetify = createVuetify({
  components,
  directives,
  theme: {
    defaultTheme: 'light',
    themes: {
      light: {
        colors: {
          primary: '#260029',
          secondary: '#ec407a',
          grey: '#9E9E9E',
          green: '#41ae57',
          blue: '#63759f',
        },
      },
    },
  },
})

// Create Pinia store
const pinia = createPinia()

// Create and mount the app
const app = createApp(App)

app.use(vuetify)
app.use(pinia)

app.mount('#app')
