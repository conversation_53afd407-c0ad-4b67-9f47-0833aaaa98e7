# Vue 2 to Vue 3 Migration Guide - ViewOrganizationDetails Component

## 📋 Overview

This document provides a comprehensive migration guide for converting the `ViewOrganizationDetails.vue.js` component from Vue 2 to Vue 3 for the hrappui-v3 repository.

## 🔍 Component Analysis

### Current Component Features
- **Report Logo Management**: Upload, change, and remove organization report logos
- **Image Cropping**: Built-in image cropping functionality using CropperJS
- **File Upload**: Drag-and-drop file upload with validation
- **Form State Management**: Dirty checking and confirmation dialogs
- **Role-based Access Control**: Permission-based UI rendering
- **S3 Integration**: Direct file upload to AWS S3 using presigned URLs

### Dependencies Identified

#### Current Vue 2 Dependencies
```javascript
// Core Framework
Vue 2.x
Vuetify 2.x

// File Upload & Image Processing
vue-upload-component@2.8.23
cropperjs@1.5.6

// HTTP & GraphQL
axios
graphql.js (f/graphql.js)

// UI Enhancements
v-tooltip@2.0.2
```

#### Vue 3 Replacements
```json
{
  "vue": "^3.4.0",
  "vuetify": "^3.4.0",
  "graphql-request": "^6.1.0",
  "cropperjs": "^1.6.1",
  "@floating-ui/vue": "^1.0.2",
  "axios": "^1.6.0"
}
```

## 🚀 Migration Steps

### 1. Install Dependencies

```bash
npm install vue@^3.4.0 vuetify@^3.4.0 @mdi/font@^7.3.67
npm install axios@^1.6.0 graphql-request@^6.1.0 cropperjs@^1.6.1
npm install @floating-ui/vue@^1.0.2 pinia@^2.1.7
```

### 2. Development Dependencies

```bash
npm install -D @vitejs/plugin-vue@^4.5.0 vite@^5.0.0
npm install -D typescript@~5.3.0 vue-tsc@^1.8.22
npm install -D @vue/tsconfig@^0.4.0
```

## 📊 API Endpoints Documentation

### GraphQL APIs

#### 1. getPresignedUrl Mutation
**Purpose**: Generate presigned URL for S3 file upload

```graphql
mutation GetPresignedUrl($fileName: String!, $action: String!, $type: String) {
  getPresignedUrl(fileName: $fileName, action: $action, type: $type) {
    errorCode
    message
    presignedUrl
  }
}
```

**Parameters**:
- `fileName`: S3 file path (e.g., "hrapp_upload/{orgCode}_tmp/logos/{filename}")
- `action`: "upload"
- `type`: "logo"

**Response**:
- `errorCode`: Error code if any
- `message`: Response message
- `presignedUrl`: S3 presigned URL for file upload

#### 2. deleteS3Files Mutation
**Purpose**: Delete files from S3 storage

```graphql
mutation DeleteS3Files($fileName: String!, $type: String) {
  deleteS3Files(fileName: $fileName, type: $type) {
    errorCode
    message
  }
}
```

**Parameters**:
- `fileName`: S3 file path to delete
- `type`: "logo"

### REST APIs

#### 1. Update Report Logo
**Endpoint**: `POST /settings/general/update-report-logo-name/`

**Payload**:
```json
{
  "logo": "ReportLogo",
  "uploadSuccessFilesArr": "filename_or_path",
  "useReportLogoAsProductLogo": "Yes|No"
}
```

**Response**:
```json
{
  "success": true|false,
  "msg": "Success/Error message"
}
```

#### 2. Remove Logo
**Endpoint**: `POST /organization/organization-settings/remove-logo/orgCode/{orgCode}`

**Response**:
```json
{
  "success": true|false,
  "msg": "Success/Error message"
}
```

## 🔄 Key Migration Changes

### 1. Component Structure
- **Vue 2**: Options API with `Vue.component()`
- **Vue 3**: Composition API with `<script setup>`

### 2. Reactivity System
- **Vue 2**: `data()` function returning reactive properties
- **Vue 3**: `ref()` and `reactive()` for reactive state

### 3. Lifecycle Hooks
- **Vue 2**: `mounted()` in options
- **Vue 3**: `onMounted()` from composition API

### 4. Template Refs
- **Vue 2**: `this.$refs.refName`
- **Vue 3**: `const refName = ref()` with template ref

### 5. Event Handling
- **Vue 2**: `this.$emit()`
- **Vue 3**: `emit()` from `defineEmits()`

### 6. Props Definition
- **Vue 2**: `props` object
- **Vue 3**: `defineProps<Interface>()` with TypeScript

## 🛠️ Implementation Details

### File Upload Component Migration

The original `vue-upload-component` has been replaced with a custom Vue 3 compatible `FileUpload.vue` component that provides:

- Drag and drop functionality
- File validation
- Multiple file support
- Progress tracking
- TypeScript support

### GraphQL Client Migration

Replaced `graphql.js` with `graphql-request` for better TypeScript support and modern API:

```typescript
// Vue 2 (Old)
const graphql = require('graphql.js')
const client = graphql(endpoint, { headers })

// Vue 3 (New)
import { GraphQLClient } from 'graphql-request'
const client = new GraphQLClient(endpoint, { headers })
```

### Vuetify 3 Changes

Key changes in Vuetify 3:
- `v-overlay` now uses `model-value` instead of `value`
- Updated component props and styling
- New theming system

## 📁 File Structure

```
src/
├── components/
│   └── FileUpload.vue          # Custom file upload component
├── composables/
│   ├── useAPI.ts              # Axios wrapper composable
│   └── useGraphQL.ts          # GraphQL client composable
├── views/
│   └── ViewOrganizationDetails.vue  # Migrated component
└── types/
    └── index.ts               # TypeScript type definitions
```

## 🧪 Testing Recommendations

### Unit Tests
1. Test file upload functionality
2. Test image cropping workflow
3. Test API integration
4. Test form validation
5. Test role-based access control

### Integration Tests
1. Test complete upload workflow
2. Test S3 integration
3. Test GraphQL mutations
4. Test error handling

### E2E Tests
1. Test user interactions
2. Test drag-and-drop functionality
3. Test form submission flows

## 🚨 Breaking Changes & Considerations

### 1. Browser Compatibility
- Vue 3 requires modern browsers (IE11 not supported)
- Ensure target browsers support ES2015+

### 2. Bundle Size
- Vue 3 has better tree-shaking
- Vuetify 3 has smaller bundle size
- Consider lazy loading for large components

### 3. Performance
- Vue 3 has better performance with Proxy-based reactivity
- Composition API allows better code organization
- Consider using `shallowRef` for large objects

## 📝 Migration Checklist

- [ ] Install Vue 3 dependencies
- [ ] Update build configuration (Vite)
- [ ] Migrate component to Composition API
- [ ] Update Vuetify components to v3
- [ ] Replace file upload library
- [ ] Update GraphQL client
- [ ] Add TypeScript types
- [ ] Test all functionality
- [ ] Update documentation
- [ ] Performance optimization

## 🔗 Additional Resources

- [Vue 3 Migration Guide](https://v3-migration.vuejs.org/)
- [Vuetify 3 Migration Guide](https://vuetifyjs.com/en/getting-started/upgrade-guide/)
- [Composition API Guide](https://vuejs.org/guide/extras/composition-api-faq.html)
- [GraphQL Request Documentation](https://github.com/prisma-labs/graphql-request)

## 🎯 Next Steps

1. Set up development environment with Vite
2. Implement the migrated component
3. Create comprehensive tests
4. Optimize for production
5. Deploy and monitor performance
