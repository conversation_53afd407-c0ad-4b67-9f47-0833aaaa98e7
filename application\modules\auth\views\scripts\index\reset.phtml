<?php
$ehrTables    = new Application_Model_DbTable_Ehr();
$orgName      = $ehrTables->organizationName();
$isProduction = Zend_Registry::get('Production');

$isDomain       = Zend_Registry::get('Domain');
$isDomainArray  = explode(".",$isDomain);

$this->headTitle()->setSeparator(' - ');
$this->headTitle($orgName . ' | HR Application');

$dbAlerts       = new Default_Model_DbTable_Alerts();
$orgCode        = $ehrTables->getOrgCode();

$dbEmpUser = new Auth_Model_DbTable_EmpUser();
$empId = $dbEmpUser->employeeId($this->userName);

if($this->result){
     echo Zend_Json::encode($this->result);
}
else{
?>

<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta content="" name="description" />
        <meta content="HRAPP" name="author" />
		
        <!--Favicon-->
        <link rel="apple-touch-icon" sizes="180x180" href="<?php echo $this->baseUrl('/apple-touch-icon.png?v=9B9bjrPr00'); ?>">
        <link rel="icon" type="image/png" sizes="32x32" href="<?php echo $this->baseUrl('/favicon-32x32.png?v=9B9bjrPr00'); ?>">
        <link rel="icon" type="image/png" sizes="16x16" href="<?php echo $this->baseUrl('/favicon-16x16.png?v=9B9bjrPr00'); ?>">
        <link rel="manifest" href="<?php echo $this->baseUrl('/site.webmanifest?v=9B9bjrPr00'); ?>">
        <link rel="mask-icon" href="<?php echo $this->baseUrl('/safari-pinned-tab.svg?v=9B9bjrPr00'); ?>" color="#5bbad5">
        <link rel="shortcut icon" href="<?php echo $this->baseUrl('/favicon.ico?v=9B9bjrPr00'); ?>">
        <meta name="apple-mobile-web-app-title" content="<?php echo strtoupper($isDomainArray[0]); ?>">
        <meta name="application-name" content="<?php echo strtoupper($isDomainArray[0]); ?>">
        <meta name="msapplication-TileColor" content="#da532c">
        <meta name="theme-color" content="#ffffff">
        
        
        <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-70x70.png'); ?>">
        <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-144x144.png'); ?>">
        <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-150x150.png'); ?>">
        <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-310x150.png'); ?>">
        <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/mstile-310x310.png'); ?>">
        <meta name="msapplication-TileImage" content="<?php echo $this->baseUrl('/browserconfig.xml'); ?>">
        <!--Favicon END-->
		
		<!--HRAPP Title Start-->
		<?php echo $this->headTitle(); ?>
		<!--HRAPP Title End-->		
        
        <link href="<?php echo $this->baseUrl('assets/global/css/custom.css?v=18'); ?>" rel="stylesheet">
		<link href="<?php echo $this->baseUrl('assets/global/css/style.css?v=4'); ?>" rel="stylesheet">
		<link href="<?php echo $this->baseUrl('assets/global/css/ui.css?v=3'); ?>" rel="stylesheet">
		<link href="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-loading/lada.min.css'); ?>" rel="stylesheet">
		<link href="<?php echo $this->baseUrl('assets/global/js/popup/style_popup.css') ?>" rel="stylesheet">
		<link href="<?php echo $this->baseUrl('assets/global/js/popup/magnific-popup.css') ?>" rel="stylesheet">
	
    </head>
    <body class="account2" data-page="reset">
        <!-- BEGIN LOGIN BOX -->
        <div class="container">
            <i class="user-img icons-faces-users-03"></i>
            
            <div class="account-form">            
                <!--<form class="form-signup" action="dashboard.html" role="form">-->
                    <form role="form" class="form-horizontal form-validation" id="editForgetPassword" method="POST" action="">
                        <h3 style="text-align: center;"><strong>Reset</strong> your password</h3>
                        <input type="hidden" name="loginEmployeeId" id="loginEmpId" value="<?php echo $empId; ?>">
                        <input type="hidden" name="paramurl" id="paramurl" value="<?php echo $this->actionurl; ?>">
                    <div class="row">                        
                        <div class="append-icon">
                            <label class="control-label col-md-4 m-b-10">Username </label>
                            <div class="control-label col-md-8 m-b-10">
                                <label class="control-label" id="formCPUserName" style="font-size:16px;" ><?php echo $this->userName; ?></label>
                            </div>
                        </div>                        
                        <div class="append-icon">
                            <label class="col-md-4 control-label m-b-10">New Password <span class="short_explanation">*</span></label>
                            <div class="col-md-8 m-b-10">
								<input type="password" class="form-control form-white password" required=true minlength="6" maxlength="30" id="formCPNewPassword" name="New_Password" placeholder="New Password">
							</div>									
                        </div>
						<div class="append-icon">
							<label class="col-md-4 control-label m-b-10">Confirm New Password <span class="short_explanation">*</span></label>
                            <div class="col-md-8 m-b-10">
                                <input type="password" class="form-control form-white password" required=true minlength="6" maxlength="30" id="formCPConfirmPassword" name="Confirm_Password" placeholder="Confirm Password">
                            </div>
                        </div>
                    </div>
                </form>
                    
                <div class="m-t-20" style="text-align:center;">
                    <button type="submit" class="btn btn-secondary btn-embossed ladda-button" data-style="expand-left" id="formForgetPasswordSubmit" style="bottom: 5px;">
                            <i class="mdi-content-send"></i> Change Password
                    </button>                       
                </div>                    
            </div>            
        </div>
        <!-- END LOCKSCREEN BOX -->
    
    <!-- <script src="<?php echo $this->baseUrl('assets/global/plugins/jquery/jquery-1.11.1.min.js'); ?>"></script> -->
    <script src="https://code.jquery.com/jquery-3.7.1.js" integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=" crossorigin="anonymous"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-migrate/3.5.2/jquery-migrate.js"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/jquery-cookies/jquery.cookies.js?v=1'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/jquery/jquery-migrate-1.2.1.min.js'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/gsap/main-gsap.min.js'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap/js/bootstrap.min.js'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/backstretch/backstretch.min.js'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/bootstrap-loading/lada.min.js'); ?>"></script>
	<script src="<?php echo $this->baseUrl('assets/global/js/popup/jquery.magnific-popup.min.js'); ?>"></script>
		
        <script type="text/javascript">
        // <![CDATA[
            $(document).ready(function(){
                if (localStorage.getItem('production') === null) {
                    localStorage.setItem('production', <?php echo $isProduction; ?>);
                }
            });
        // ]]>
        </script>        
        
		<script src="<?php echo $this->baseUrl('assets/global/js/custom/custom.js?v=132'); ?>"></script>		
		<script src="<?php echo $this->baseUrl('assets/global/plugins/noty/jquery.noty.packaged.js'); ?>"></script>
		<script src="<?php echo $this->baseUrl('assets/global/plugins/jquery-validation/jquery.validate.js'); ?>"></script>
        <script src="<?php echo $this->baseUrl('assets/global/plugins/jquery-validation/jquery.validate.min.js'); ?>"></script>
		<script src="<?php echo $this->baseUrl('assets/global/plugins/jquery-validation/additional-methods.js'); ?>"></script>
        <input type="hidden" id="backbuttonrefresh" value="no">
  </body>
</html>

<?php } ?>