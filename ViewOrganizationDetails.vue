<template>
  <section class="component-padding" style="margin-top: 45px">
    <v-card class="card-radius view-org-details-card">
      <v-card-title class="d-flex justify-end">
        <!-- Edit/Save/Cancel buttons based on state -->
        <div v-if="isViewPage && reportLogoURL && !showSave">
          <v-btn
            v-if="rolesResponse.Role_Update"
            rounded
            color="primary"
            @click="showEditForm"
          >
            Edit
          </v-btn>
          <v-btn
            v-else
            rounded
            style="cursor: not-allowed"
            v-tooltip="'You dont have edit access rights'"
          >
            Edit
          </v-btn>
        </div>
        <div v-else-if="!reportLogoURL && isViewPage && !showSave">
          <v-btn
            v-if="rolesResponse.Role_Update"
            rounded
            color="primary"
            @click="saveOrgDetails"
          >
            Save
          </v-btn>
          <v-btn
            v-else
            rounded
            style="cursor: not-allowed"
            v-tooltip="'You dont have edit access rights'"
          >
            Save
          </v-btn>
        </div>
        <div v-else>
          <v-btn
            rounded
            outlined
            color="primary"
            class="mr-5"
            @click="closeEditForm"
          >
            Cancel
          </v-btn>
          <v-btn
            v-if="rolesResponse.Role_Update"
            rounded
            color="primary"
            @click="saveOrgDetails"
          >
            Save
          </v-btn>
          <v-btn
            v-else
            rounded
            style="cursor: not-allowed"
            v-tooltip="'You dont have edit access rights'"
          >
            Save
          </v-btn>
        </div>
      </v-card-title>

      <v-card-text class="view-card-text">
        <div class="secondaryColor font-weight-bold body-1">Report Logo</div>
        
        <!-- File Upload Area -->
        <div class="example-avatar" style="display: flex">
          <div v-show="uploadRef?.dropActive" class="drop-active">
            <h3>Drop files to upload</h3>
          </div>
          
          <div class="avatar-upload" v-show="!edit">
            <div class="text-center p-10">
              <label for="avatar">
                <img
                  :src="reportLogoSrc"
                  class="rounded-circle"
                  @error="errorReportImg"
                />
              </label>
            </div>
            
            <div
              class="text-center p-2"
              v-if="(!reportLogoURL && isViewPage) || (reportLogoURL && !isViewPage)"
            >
              <!-- Vue 3 File Upload Component -->
              <FileUpload
                ref="uploadRef"
                v-model="files"
                :extensions="['jpg', 'jpeg', 'png']"
                accept="image/png,image/jpeg"
                name="report logo"
                class="btn btn-secondary"
                :drop="!edit"
                @input-filter="inputFilter"
                @input-file="inputFile"
              >
                <span style="text-transform: capitalize !important">
                  {{ uploadBtnText }}
                </span>
              </FileUpload>
              
              <v-btn
                v-if="rolesResponse.Role_Update && reportLogoURL"
                rounded
                color="primary"
                @click="removeLogo"
              >
                Remove Logo
              </v-btn>
              <v-btn
                v-else-if="reportLogoURL"
                rounded
                style="cursor: not-allowed"
                v-tooltip="'You dont have edit access rights'"
              >
                Remove Logo
              </v-btn>
            </div>
            
            <!-- Product Logo Switch -->
            <v-switch
              v-if="!reportLogoURL && isViewPage && !showSave"
              color="primary"
              class="p-2"
              :disabled="!reportLogoURL && !uploadFileName"
              label="Use Report Logo As Product Logo"
              v-model="isProductLogo"
              @change="isFormDirty = true"
              :true-value="1"
              :false-value="0"
            />
            <v-switch
              v-else
              color="primary"
              class="p-2"
              :disabled="!rolesResponse.Role_Update || !reportLogoURL || isViewPage"
              label="Use Report Logo As Product Logo"
              v-model="isProductLogo"
              @change="isFormDirty = true"
              :true-value="1"
              :false-value="0"
            />
          </div>
        </div>
        
        <!-- Image Cropping Area -->
        <div class="avatar-edit" v-show="files.length && edit" style="overflow: hidden">
          <div class="avatar-edit-image" v-if="files.length" style="width: 100%; height: 400px">
            <img ref="editImageRef" :src="files[0]?.url" />
          </div>
          <div class="text-center p-10">
            <v-btn rounded outlined color="primary" class="mr-5" @click="cancelCrop">
              Cancel
            </v-btn>
            <v-btn rounded color="primary" @click="editSave">Save</v-btn>
          </div>
        </div>
        
        <!-- Loading/Confirmation Overlay -->
        <v-overlay
          :model-value="isLoading || showFormDirty"
          :absolute="true"
          opacity="0.9"
          color="#fff"
        >
          <v-progress-circular
            v-if="isLoading"
            color="primary"
            indeterminate
            size="64"
          />
          <div v-else>
            <v-row class="set-layout">
              <v-col cols="12" class="d-flex flex-column justify-center align-center">
                <div class="mb-5 d-flex flex-column justify-center align-center">
                  <img
                    width="60%"
                    height="auto"
                    :src="basePath + 'vue/assets/images/exit_form.webp'"
                    @error="imageFormatSwap"
                    alt="No Records"
                  />
                </div>
                <span class="mb-5 sub-content view-error-message primary--text" style="text-align: center">
                  Your changes won't be saved. Are you sure want to close this form?
                </span>
                <div class="mb-4">
                  <v-row>
                    <v-col xl="6" lg="6" md="6" sm="6" cols="6">
                      <v-btn rounded color="primary" @click="showFormDirty = false">
                        No
                      </v-btn>
                    </v-col>
                    <v-col xl="6" lg="6" md="6" sm="6" cols="6">
                      <v-btn rounded outlined color="primary" @click="confirmFormClose">
                        Yes
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </v-col>
            </v-row>
          </div>
        </v-overlay>
      </v-card-text>
    </v-card>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useGraphQL } from '@/composables/useGraphQL'
import { useAPI } from '@/composables/useAPI'
import FileUpload from '@/components/FileUpload.vue'
import Cropper from 'cropperjs'

// Props
interface Props {
  logoUrl: string
  orgCode: string
  employeeId: number
  renderCount: number
  rolesResponse: Record<string, any>
  basePath: string
  atsBaseUrl: string
  apiHeaders: Record<string, any>
  useReportLogoAsProductLogo: string
  reportLogoPath?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'handle-custom-error': [message: string]
  'handle-error': [error: any]
  'handle-success': [message: string]
}>()

// Reactive state
const isFormDirty = ref(false)
const isLoading = ref(false)
const showFormDirty = ref(false)
const isViewPage = ref(true)
const showSave = ref(false)
const reportLogoURL = ref('')
const files = ref<any[]>([])
const edit = ref(false)
const cropper = ref<Cropper | null>(null)
const uploadFileName = ref('')
const uploadedFile = ref<File | null>(null)
const operationType = ref('add')
const isProductLogo = ref(0)

// Refs
const uploadRef = ref()
const editImageRef = ref()

// Composables
const { graphQLClient } = useGraphQL(props.atsBaseUrl, props.apiHeaders)
const { apiClient } = useAPI()

// Computed properties
const uploadBtnText = computed(() => {
  if (!reportLogoURL.value && files.value.length === 0) {
    return 'Set Logo'
  } else {
    return 'Change'
  }
})

const reportLogoSrc = computed(() => {
  if (operationType.value !== 'remove') {
    if (files.value.length) {
      return files.value[0].url
    }
    return reportLogoURL.value
      ? reportLogoURL.value
      : props.basePath + 'vue/assets/images/empty-image.webp'
  } else {
    return props.basePath + 'vue/assets/images/empty-image.webp'
  }
})

const errorReportImg = computed(() => {
  if (operationType.value !== 'remove') {
    if (files.value.length) {
      return files.value[0].url
    }
    return reportLogoURL.value
      ? reportLogoURL.value
      : props.basePath + 'vue/assets/images/empty-image.png'
  } else {
    return props.basePath + 'vue/assets/images/empty-image.png'
  }
})

// Utility functions
const baseUrl = () => {
  const pathParts = location.pathname.split('/')
  if (localStorage.getItem('production') == '0') {
    return location.origin + '/' + pathParts[1].trim('/') + '/'
  } else {
    return location.origin + '/'
  }
}

// Methods
const showEditForm = () => {
  showSave.value = true
  isViewPage.value = false
  operationType.value = 'edit'
}

const closeEditForm = () => {
  if (isFormDirty.value && operationType.value !== 'remove') {
    showFormDirty.value = true
  } else {
    resetForm()
  }
}

const resetForm = () => {
  isFormDirty.value = false
  showFormDirty.value = false
  isViewPage.value = true
  showSave.value = false
  files.value = []
  edit.value = false
  operationType.value = 'edit'
  reportLogoURL.value = props.logoUrl
}

const confirmFormClose = () => {
  isFormDirty.value = false
  showFormDirty.value = false
  closeEditForm()
}

const saveOrgDetails = () => {
  if (isFormDirty.value) {
    updateOrgDetails()
  } else {
    emit('handle-custom-error', 'Form has no changes')
  }
}

const updateOrgDetails = async () => {
  try {
    isLoading.value = true

    if (operationType.value !== 'remove') {
      if (uploadFileName.value && !edit.value) {
        await handleFileUpload()
      } else {
        if (!edit.value) {
          await updateReportLogo()
        } else {
          isLoading.value = false
          emit('handle-custom-error', 'Form has no changes')
        }
      }
    } else {
      await handleFileRemoval()
    }
  } catch (error) {
    isLoading.value = false
    emit('handle-error', error)
  }
}

const handleFileUpload = async () => {
  try {
    const fileName = `hrapp_upload/${props.orgCode}_tmp/logos/${uploadFileName.value}`

    const response = await graphQLClient.request(`
      mutation($fileName: String!, $action: String!, $type: String) {
        getPresignedUrl(fileName: $fileName, action: $action, type: $type) {
          errorCode
          message
          presignedUrl
        }
      }
    `, {
      fileName,
      action: 'upload',
      type: 'logo'
    })

    if (response.getPresignedUrl?.presignedUrl) {
      reportLogoURL.value = response.getPresignedUrl.presignedUrl

      await apiClient.put(response.getPresignedUrl.presignedUrl, uploadedFile.value)
      await updateReportLogo()
    } else {
      isLoading.value = false
      emit('handle-custom-error', 'Unable to upload report logo')
    }
  } catch (error) {
    isLoading.value = false
    emit('handle-custom-error', 'Unable to upload report logo')
  }
}

const handleFileRemoval = async () => {
  try {
    const fileName = `hrapp_upload/${props.orgCode}_tmp/logos/${uploadFileName.value}`

    await graphQLClient.request(`
      mutation($fileName: String!, $type: String) {
        deleteS3Files(fileName: $fileName, type: $type) {
          errorCode
          message
        }
      }
    `, {
      fileName,
      type: 'logo'
    })

    const response = await apiClient.post(
      `${baseUrl()}organization/organization-settings/remove-logo/orgCode/${props.orgCode}`,
      {}
    )

    reportLogoURL.value = ''

    if (response.data.success) {
      resetForm()
      emit('handle-success', response.data.msg)
    } else {
      emit('handle-custom-error', response.data.msg)
    }

    isLoading.value = false
  } catch (error) {
    isLoading.value = false
    emit('handle-custom-error', 'Unable to delete report logo')
  }
}

const updateReportLogo = async () => {
  try {
    const response = await apiClient.post(
      `${baseUrl()}settings/general/update-report-logo-name/`,
      {
        logo: 'ReportLogo',
        uploadSuccessFilesArr: uploadFileName.value || props.reportLogoPath,
        useReportLogoAsProductLogo: isProductLogo.value === 0 ? 'No' : 'Yes'
      }
    )

    if (response.data.success) {
      resetForm()
      emit('handle-success', response.data.msg)
    } else {
      emit('handle-custom-error', response.data.msg)
    }

    isLoading.value = false
  } catch (error) {
    isLoading.value = false
    emit('handle-custom-error', 'Unable to upload report logo')
  }
}

const removeLogo = () => {
  isFormDirty.value = true
  showSave.value = true
  operationType.value = 'remove'
}

const imageFormatSwap = (e: Event) => {
  const target = e.target as HTMLImageElement
  target.src = props.basePath + 'vue/assets/images/exit_form.png'
}

const editSave = () => {
  edit.value = false
  isFormDirty.value = true
  operationType.value = 'edit'

  const oldFile = files.value[0]
  const binStr = atob(cropper.value!.getCroppedCanvas().toDataURL(oldFile.type).split(',')[1])
  const arr = new Uint8Array(binStr.length)

  for (let i = 0; i < binStr.length; i++) {
    arr[i] = binStr.charCodeAt(i)
  }

  const file = new File([arr], oldFile.name, { type: oldFile.type })

  uploadRef.value?.update(oldFile.id, {
    file,
    type: file.type,
    size: file.size,
    active: true,
  })

  uploadedFile.value = file
  const fileName = file.name
  const reportLogoImageExtension = fileName.split('.')
  uploadFileName.value = `${props.orgCode}-Report_LogoPath.${reportLogoImageExtension[1]}`
}

const cancelCrop = () => {
  uploadRef.value?.clear()
}

const inputFile = (newFile: any, oldFile: any) => {
  if (newFile && !oldFile) {
    nextTick(() => {
      edit.value = true
    })
  }
  if (!newFile && oldFile) {
    edit.value = false
  }
}

const inputFilter = (newFile: any, oldFile: any, prevent: Function) => {
  if (newFile && !oldFile) {
    if (!/\.(gif|jpg|jpeg|png)$/i.test(newFile.name)) {
      emit('handle-custom-error', 'Invalid file type selected')
      return prevent()
    }
  }

  if (newFile && (!oldFile || newFile.file !== oldFile.file)) {
    newFile.url = ''
    const URL = window.URL || (window as any).webkitURL
    if (URL && URL.createObjectURL) {
      newFile.url = URL.createObjectURL(newFile.file)
    }
  }
}

// Watchers
watch(edit, (value) => {
  if (value) {
    nextTick(() => {
      if (!editImageRef.value) return

      cropper.value = new Cropper(editImageRef.value, {
        aspectRatio: 16 / 9,
        viewMode: 1,
      })
    })
  } else {
    if (cropper.value) {
      cropper.value.destroy()
      cropper.value = null
    }
  }
})

// Lifecycle
onMounted(() => {
  reportLogoURL.value = props.logoUrl
  isProductLogo.value = props.useReportLogoAsProductLogo?.toLowerCase() === 'yes' ? 1 : 0

  if (props.renderCount === 1) {
    // Add component styles
    const styleElem = document.createElement('style')
    styleElem.textContent = `
      .component-padding {
        padding: 50px;
      }
      .view-org-details-card {
        padding: 2em;
        min-height: 600px;
      }
      .view-error-message {
        font-size: 1.4em !important;
        font-weight: 500;
      }
      label {
        margin-bottom: 0px !important;
      }
      .v-application--is-ltr .v-messages {
        display: contents !important;
      }
      @media screen and (max-width: 600px) {
        .component-padding {
          padding-left: 25px !important;
          padding-right: 25px !important;
        }
      }
      .example-avatar .avatar-upload .rounded-circle {
        width: 200px;
      }
      .example-avatar .text-center .btn {
        margin: 0 0.5rem;
      }
      .example-avatar .avatar-edit-image {
        max-width: 100%;
      }
      .example-avatar .drop-active {
        top: 0;
        bottom: 0;
        right: 0;
        left: 0;
        position: fixed;
        z-index: 9999;
        opacity: 0.6;
        text-align: center;
        background: #000;
      }
      .example-avatar .drop-active h3 {
        margin: -0.5em 0 0;
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        font-size: 40px;
        color: #fff;
        padding: 0;
      }
    `
    document.head.appendChild(styleElem)
  }
})
</script>

<style scoped>
/* Component-specific styles */
</style>
