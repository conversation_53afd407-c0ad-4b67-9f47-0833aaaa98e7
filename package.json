{"name": "hrappui-v3", "version": "1.0.0", "description": "HRAPP UI Vue 3 Migration", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vuetify": "^3.4.0", "@mdi/font": "^7.3.67", "axios": "^1.6.0", "graphql-request": "^6.1.0", "cropperjs": "^1.6.1", "@floating-ui/vue": "^1.0.2", "pinia": "^2.1.7"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "typescript": "~5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}}